# Instagram Reels Collector

A responsive web application for collecting, organizing, and managing your favorite Instagram reels with download capabilities.

## Features

### Core Functionality
- **Add Reel Form**: Easy-to-use modal form for adding new reels
- **Card Display**: Pinterest-style layout for displaying reel collections
- **Detail View**: Comprehensive detail modal with image preview and actions
- **Local Storage**: Persistent data storage in browser
- **Search & Filter**: Search by title/tags and filter by categories

### User Interface
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Modern Animations**: Smooth transitions and micro-animations
- **Featured Carousel**: Swiper.js carousel for recent additions
- **Toast Notifications**: User feedback for all actions
- **Loading States**: Visual feedback during operations

### External Integrations
- **ImgBB**: Image hosting for reel previews
- **SSS Instagram**: Video download integration
- **LottieFiles**: Engaging micro-animations
- **Font Awesome**: Professional icons

## Technologies Used

- **HTML5**: Semantic markup and accessibility
- **CSS3**: Modern styling with CSS Grid and Flexbox
- **JavaScript (ES6+)**: Modern JavaScript features and APIs
- **Swiper.js**: Touch-enabled carousel/slider
- **LottieFiles**: Animation library
- **Font Awesome**: Icon library
- **Google Fonts**: Typography (Inter font family)

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection for external libraries and image loading

### Installation
1. Clone or download the project files
2. Ensure all files are in the same directory:
   - `index.html`
   - `styles.css`
   - `script.js`
   - `README.md`

### Running the Application
1. **Option 1: Direct File Opening**
   - Open `index.html` directly in your web browser
   - Note: Some features may be limited due to CORS restrictions

2. **Option 2: Local Server (Recommended)**
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx http-server -p 8000
   
   # Using PHP
   php -S localhost:8000
   ```
   Then open `http://localhost:8000` in your browser

## Usage Guide

### Adding a New Reel
1. Click the "Add Link" button in the header
2. Fill in the required fields:
   - **Title**: Descriptive name for the reel
   - **Image Preview URL**: Direct link to image (use ImgBB for hosting)
   - **Instagram Reel URL**: Full Instagram reel URL
   - **Tags**: Comma-separated tags for organization
3. Click "Save Reel" to add to your collection

### Managing Your Collection
- **Search**: Use the search bar to find reels by title or tags
- **Filter**: Click on tag filters to view specific categories
- **View Details**: Click on any card to open the detail modal
- **Quick Actions**: Use card action buttons for instant access

### Detail View Actions
- **Open Link**: Opens the original Instagram reel
- **Copy Link**: Copies the reel URL to clipboard
- **Download Video**: Redirects to SSS Instagram downloader

## File Structure
```
instareelscollector/
├── index.html          # Main HTML file
├── styles.css          # CSS styles and responsive design
├── script.js           # JavaScript functionality
└── README.md           # Documentation
```

## Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Features in Detail

### Responsive Design
- **Mobile (< 768px)**: Single column layout, touch-optimized
- **Tablet (768px - 1024px)**: Multi-column grid, balanced layout
- **Desktop (> 1024px)**: Full Pinterest-style grid layout

### Data Storage
- Uses browser's localStorage for data persistence
- Data survives browser restarts and page refreshes
- No server-side storage required

### Performance Optimizations
- Lazy loading for images
- Intersection Observer for animations
- Efficient DOM manipulation
- Optimized CSS animations

## Customization

### Styling
Modify `styles.css` to customize:
- Color scheme (CSS custom properties in `:root`)
- Layout dimensions and spacing
- Animation timings and effects
- Typography and fonts

### Functionality
Extend `script.js` to add:
- Additional reel metadata fields
- Export/import functionality
- Social sharing features
- Advanced filtering options

## Troubleshooting

### Common Issues
1. **Images not loading**: Ensure ImgBB URLs are direct image links
2. **Instagram URLs invalid**: Use full Instagram reel URLs
3. **Local storage full**: Clear browser data or export/backup reels
4. **Animations not working**: Ensure JavaScript is enabled

### Browser Console
Check browser developer tools console for error messages and debugging information.

## Contributing
This is a standalone project. Feel free to fork and modify according to your needs.

## License
This project is open source and available under the MIT License.

## Support
For issues or questions, please check the browser console for error messages and ensure all external libraries are loading properly.
