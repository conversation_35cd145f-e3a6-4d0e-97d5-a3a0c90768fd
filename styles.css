/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #e91e63;
    --primary-dark: #c2185b;
    --secondary-color: #673ab7;
    --accent-color: #ff4081;
    --background-color: #fafafa;
    --surface-color: #ffffff;
    --text-primary: #212121;
    --text-secondary: #757575;
    --border-color: #e0e0e0;
    --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 16px rgba(0,0,0,0.15);
    --shadow-heavy: 0 8px 32px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--background-color);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-medium);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.add-btn {
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.add-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.add-btn i {
    margin-right: 0.5rem;
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 80px);
}

/* Search Section */
.search-section {
    margin-bottom: 2rem;
}

.search-container {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.search-box {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--surface-color);
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.filter-tag {
    background: var(--surface-color);
    border: 2px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.875rem;
    font-weight: 500;
}

.filter-tag:hover,
.filter-tag.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    display: none;
}

.empty-state.show {
    display: block;
}

.empty-content h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.empty-content p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.lottie-container {
    width: 200px;
    height: 200px;
    margin: 0 auto 2rem;
}

.cta-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-medium);
}

.cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
}

.cta-btn i {
    margin-right: 0.5rem;
}

/* Featured Section */
.featured-section {
    margin-bottom: 3rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-title i {
    color: var(--accent-color);
}

/* Swiper Styles */
.featured-swiper {
    padding: 1rem 0 3rem;
}

.featured-swiper .swiper-slide {
    width: 280px;
    height: auto;
}

.featured-slide {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
}

.featured-slide:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.featured-slide-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.featured-slide-content {
    padding: 1rem;
}

.featured-slide-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.featured-slide-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.featured-slide-tag {
    background: var(--background-color);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Swiper Navigation */
.featured-swiper .swiper-button-next,
.featured-swiper .swiper-button-prev {
    color: var(--primary-color);
    background: var(--surface-color);
    width: 44px;
    height: 44px;
    border-radius: 50%;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
}

.featured-swiper .swiper-button-next:hover,
.featured-swiper .swiper-button-prev:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.featured-swiper .swiper-button-next::after,
.featured-swiper .swiper-button-prev::after {
    font-size: 18px;
    font-weight: 700;
}

.featured-swiper .swiper-pagination-bullet {
    background: var(--primary-color);
    opacity: 0.3;
    transition: var(--transition);
}

.featured-swiper .swiper-pagination-bullet-active {
    opacity: 1;
    transform: scale(1.2);
}

/* Reels Grid - Pinterest Style */
.reels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

.reel-card {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    break-inside: avoid;
}

.reel-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.reel-card-image {
    width: 100%;
    aspect-ratio: 9/16;
    object-fit: cover;
    transition: var(--transition);
}

.reel-card:hover .reel-card-image {
    transform: scale(1.05);
}

.reel-card-content {
    padding: 1rem;
}

.reel-card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.reel-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.reel-tag {
    background: var(--background-color);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
}

.reel-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.reel-card-date {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.reel-card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-action-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    backdrop-filter: blur(5px);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9) translateY(20px);
    transition: var(--transition);
    box-shadow: var(--shadow-heavy);
}

.modal-overlay.show .modal {
    transform: scale(1) translateY(0);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition);
}

.close-btn:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

/* Form Styles */
.modal-form {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background: var(--surface-color);
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
}

.form-group small {
    display: block;
    margin-top: 0.25rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.cancel-btn,
.submit-btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    font-size: 1rem;
}

.cancel-btn {
    background: var(--background-color);
    color: var(--text-secondary);
}

.cancel-btn:hover {
    background: var(--border-color);
}

.submit-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    box-shadow: var(--shadow-medium);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.submit-btn i {
    margin-right: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .logo h1 {
        font-size: 1.25rem;
    }
    
    .reels-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .modal {
        width: 95%;
        margin: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .search-container {
        align-items: center;
    }
}

@media (max-width: 480px) {
    .reels-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .reel-card-content {
        padding: 0.75rem;
    }
    
    .empty-content h2 {
        font-size: 1.5rem;
    }
    
    .empty-content p {
        font-size: 1rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading Spinner */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.loading-spinner.show {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Detail Modal Styles */
.detail-modal {
    background: var(--surface-color);
    border-radius: var(--border-radius);
    width: 95%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: var(--transition);
    box-shadow: var(--shadow-heavy);
    position: relative;
}

.modal-overlay.show .detail-modal {
    transform: scale(1) translateY(0);
}

.detail-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 10;
    background: rgba(0,0,0,0.5);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.detail-close:hover {
    background: rgba(0,0,0,0.7);
    transform: scale(1.1);
}

.detail-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    height: 80vh;
}

.detail-left {
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.image-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    animation: zoomPulse 3s ease-in-out infinite;
}

@keyframes zoomPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.zoom-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, transparent 30%, rgba(0,0,0,0.3) 100%);
    pointer-events: none;
}

.detail-right {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    overflow-y: auto;
}

.detail-info h2 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
    line-height: 1.3;
}

.detail-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.detail-tag {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
}

.detail-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
}

.action-btn.primary {
    background: linear-gradient(135deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    color: white;
    box-shadow: var(--shadow-medium);
}

.action-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.action-btn.secondary {
    background: var(--background-color);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.action-btn.secondary:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.action-btn.download {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    box-shadow: var(--shadow-medium);
}

.action-btn.download:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.detail-meta {
    background: var(--background-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

.detail-meta p {
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

.detail-meta p:last-child {
    margin-bottom: 0;
}

.detail-meta strong {
    color: var(--text-primary);
}

.detail-meta span {
    word-break: break-all;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background: var(--surface-color);
    border-left: 4px solid var(--primary-color);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    min-width: 300px;
    transform: translateX(100%);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #4CAF50;
}

.toast.error {
    border-left-color: #f44336;
}

.toast.info {
    border-left-color: #2196F3;
}

.toast-icon {
    font-size: 1.25rem;
}

.toast.success .toast-icon {
    color: #4CAF50;
}

.toast.error .toast-icon {
    color: #f44336;
}

.toast.info .toast-icon {
    color: #2196F3;
}

.toast-message {
    flex: 1;
    font-weight: 500;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition);
}

.toast-close:hover {
    background: var(--background-color);
    color: var(--text-primary);
}

/* Mobile Responsive for Detail Modal */
@media (max-width: 768px) {
    .detail-content {
        grid-template-columns: 1fr;
        height: auto;
    }

    .detail-left {
        height: 50vh;
    }

    .detail-right {
        padding: 1.5rem;
    }

    .detail-info h2 {
        font-size: 1.5rem;
    }

    .action-btn {
        padding: 0.875rem;
        font-size: 0.9rem;
    }

    .toast-container {
        right: 10px;
        left: 10px;
    }

    .toast {
        min-width: auto;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
button:focus,
input:focus,
.reel-card:focus {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-light: 0 2px 8px rgba(0,0,0,0.3);
        --shadow-medium: 0 4px 16px rgba(0,0,0,0.4);
        --shadow-heavy: 0 8px 32px rgba(0,0,0,0.5);
    }
}

/* Print styles */
@media print {
    .header,
    .modal-overlay,
    .toast-container,
    .loading-spinner {
        display: none !important;
    }

    .reels-grid {
        display: block !important;
    }

    .reel-card {
        break-inside: avoid;
        margin-bottom: 1rem;
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #121212;
        --surface-color: #1e1e1e;
        --text-primary: #ffffff;
        --text-secondary: #b3b3b3;
        --border-color: #333333;
    }

    .reel-card-image {
        opacity: 0.9;
    }

    .modal {
        background: var(--surface-color);
        color: var(--text-primary);
    }
}

/* Performance optimizations */
.reel-card-image,
.featured-slide-image,
#detailImage {
    will-change: transform;
    backface-visibility: hidden;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading skeleton animation */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
