<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ImgBB URL Demo - Instagram Reels Collector</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #fafafa;
        }
        .demo-container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .example {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid #e91e63;
        }
        .url-input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            margin: 0.5rem 0;
        }
        .btn {
            background: linear-gradient(135deg, #e91e63, #ff4081);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
        }
        .result {
            background: #e8f5e8;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            border-left: 4px solid #4caf50;
        }
        .error {
            background: #ffeaea;
            border-left-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🖼️ ImgBB URL Processing Demo</h1>
        <p>This demo shows how the Instagram Reels Collector automatically converts ImgBB sharing URLs to direct image URLs.</p>
        
        <div class="example">
            <h3>✅ Supported URL Formats:</h3>
            <ul>
                <li><strong>ImgBB Sharing URL:</strong> <code>https://ibb.co/HTvR20N</code></li>
                <li><strong>Direct Image URL:</strong> <code>https://i.ibb.co/HTvR20N.jpg</code></li>
                <li><strong>Other Direct URLs:</strong> Any URL ending in .jpg, .png, .jpeg, .webp</li>
            </ul>
        </div>

        <div class="example">
            <h3>🔄 How It Works:</h3>
            <ol>
                <li>You paste an ImgBB sharing URL like: <code>https://ibb.co/HTvR20N</code></li>
                <li>The app detects it's an ImgBB URL</li>
                <li>It automatically converts to: <code>https://i.ibb.co/HTvR20N.jpg</code></li>
                <li>The app tests multiple formats (.jpg, .png, .jpeg, .webp)</li>
                <li>Uses the first working direct URL</li>
            </ol>
        </div>

        <h3>🧪 Test URL Conversion:</h3>
        <input type="url" class="url-input" id="testUrl" placeholder="Paste an ImgBB URL here (e.g., https://ibb.co/HTvR20N)">
        <br>
        <button class="btn" onclick="testConversion()">Test Conversion</button>
        
        <div id="result"></div>

        <div class="example">
            <h3>📝 Usage in Main App:</h3>
            <p>In the main Instagram Reels Collector app:</p>
            <ol>
                <li>Click "Add Link" button</li>
                <li>Paste your ImgBB sharing URL in the "Image Preview URL" field</li>
                <li>The app will automatically convert it to a direct image URL</li>
                <li>You'll see a "Processing ImgBB URL..." notification</li>
                <li>Once processed, your reel will be saved with the direct image URL</li>
            </ol>
        </div>

        <p><a href="index.html" style="color: #e91e63; text-decoration: none; font-weight: 600;">← Back to Main App</a></p>
    </div>

    <script>
        function testConversion() {
            const url = document.getElementById('testUrl').value;
            const resultDiv = document.getElementById('result');
            
            if (!url) {
                resultDiv.innerHTML = '<div class="result error">Please enter a URL to test.</div>';
                return;
            }

            // Check if it's an ImgBB URL
            const imgbbPattern = /^https?:\/\/ibb\.co\/([A-Za-z0-9]+)/;
            const match = url.match(imgbbPattern);
            
            if (match) {
                const imageId = match[1];
                const directUrl = `https://i.ibb.co/${imageId}.jpg`;

                // Test multiple formats
                const testFormats = [
                    `https://i.ibb.co/${imageId}.jpg`,
                    `https://i.ibb.co/${imageId}.png`,
                    `https://i.ibb.co/${imageId}.jpeg`,
                    `https://i.ibb.co/${imageId}.webp`
                ];

                resultDiv.innerHTML = `
                    <div class="result">
                        <h4>✅ Conversion Successful!</h4>
                        <p><strong>Original URL:</strong> ${url}</p>
                        <p><strong>Primary Direct URL:</strong> ${directUrl}</p>
                        <p><strong>Image ID:</strong> ${imageId}</p>
                        <div style="margin-top: 1rem;">
                            <p><strong>Testing formats:</strong></p>
                            ${testFormats.map(testUrl => `
                                <div style="margin: 0.5rem 0; padding: 0.5rem; background: #f8f9fa; border-radius: 4px;">
                                    <code>${testUrl}</code>
                                    <img src="${testUrl}" alt="Test" style="max-width: 100px; margin-left: 1rem; border-radius: 4px;"
                                         onload="this.nextElementSibling.innerHTML='✅ Works'; this.nextElementSibling.style.color='green';"
                                         onerror="this.nextElementSibling.innerHTML='❌ Failed'; this.nextElementSibling.style.color='red';">
                                    <span style="margin-left: 0.5rem;">Testing...</span>
                                </div>
                            `).join('')}
                        </div>
                        <p style="margin-top: 1rem; font-size: 0.9rem; color: #666;">
                            💡 The app will automatically use the first working format.
                        </p>
                    </div>
                `;
            } else {
                // Check if it's already a direct URL
                if (url.match(/\.(jpg|jpeg|png|gif|webp)$/i)) {
                    resultDiv.innerHTML = `
                        <div class="result">
                            <h4>ℹ️ Already a Direct URL</h4>
                            <p>This appears to be a direct image URL. No conversion needed.</p>
                            <p><strong>URL:</strong> ${url}</p>
                            <img src="${url}" alt="Direct image" style="max-width: 200px; border-radius: 8px; margin-top: 1rem;" 
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                            <p style="display: none; color: #f44336;">⚠️ Image failed to load. Please check the URL.</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h4>❌ Unsupported URL Format</h4>
                            <p>This doesn't appear to be a valid ImgBB sharing URL or direct image URL.</p>
                            <p><strong>Expected format:</strong> https://ibb.co/xxxxx</p>
                            <p><strong>Your URL:</strong> ${url}</p>
                        </div>
                    `;
                }
            }
        }

        // Allow Enter key to trigger test
        document.getElementById('testUrl').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testConversion();
            }
        });
    </script>
</body>
</html>
