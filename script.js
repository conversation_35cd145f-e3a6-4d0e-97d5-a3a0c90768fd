// Instagram Reels Collector - Main JavaScript File

class ReelsCollector {
    constructor() {
        this.reels = this.loadReels();
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.swiper = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.renderReels();
        this.initLottieAnimations();
        this.updateFilterTags();
        this.initSwiper();
        this.addMotionEffects();
    }

    bindEvents() {
        // Modal events
        document.getElementById('addReelBtn').addEventListener('click', () => this.openAddModal());
        document.getElementById('closeModalBtn').addEventListener('click', () => this.closeAddModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeAddModal());
        document.getElementById('closeDetailBtn').addEventListener('click', () => this.closeDetailModal());
        
        // Form submission
        document.getElementById('addReelForm').addEventListener('submit', (e) => this.handleFormSubmit(e));
        
        // Search functionality
        document.getElementById('searchInput').addEventListener('input', (e) => this.handleSearch(e));
        
        // Close modals on overlay click
        document.getElementById('addReelModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.closeAddModal();
        });
        document.getElementById('reelDetailModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) this.closeDetailModal();
        });

        // Keyboard events
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAddModal();
                this.closeDetailModal();
            }
        });
    }

    // Local Storage Management
    loadReels() {
        const stored = localStorage.getItem('instagramReels');
        return stored ? JSON.parse(stored) : [];
    }

    saveReels() {
        localStorage.setItem('instagramReels', JSON.stringify(this.reels));
    }

    // Modal Management
    openAddModal() {
        const modal = document.getElementById('addReelModal');
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
        
        // Focus on first input
        setTimeout(() => {
            document.getElementById('reelTitle').focus();
        }, 300);
    }

    closeAddModal() {
        const modal = document.getElementById('addReelModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
        
        // Reset form
        document.getElementById('addReelForm').reset();
    }

    openDetailModal(reel) {
        const modal = document.getElementById('reelDetailModal');
        
        // Populate modal content
        document.getElementById('detailImage').src = reel.image;
        document.getElementById('detailTitle').textContent = reel.title;
        document.getElementById('detailUrl').textContent = reel.url;
        document.getElementById('detailDate').textContent = new Date(reel.dateAdded).toLocaleDateString();
        
        // Populate tags
        const tagsContainer = document.getElementById('detailTags');
        tagsContainer.innerHTML = '';
        reel.tags.forEach(tag => {
            const tagElement = document.createElement('span');
            tagElement.className = 'detail-tag';
            tagElement.textContent = tag;
            tagsContainer.appendChild(tagElement);
        });
        
        // Bind action buttons
        document.getElementById('openLinkBtn').onclick = () => window.open(reel.url, '_blank');
        document.getElementById('copyLinkBtn').onclick = () => this.copyToClipboard(reel.url);
        document.getElementById('downloadBtn').onclick = () => this.downloadVideo(reel.url);
        
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }

    closeDetailModal() {
        const modal = document.getElementById('reelDetailModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }

    // Form Handling
    async handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const reelData = {
            id: Date.now().toString(),
            title: formData.get('title').trim(),
            image: formData.get('image').trim(),
            url: formData.get('url').trim(),
            tags: formData.get('tags') ? formData.get('tags').split(',').map(tag => tag.trim()).filter(tag => tag) : [],
            dateAdded: new Date().toISOString()
        };

        // Validate required fields
        if (!reelData.title || !reelData.image || !reelData.url) {
            this.showToast('Please fill in all required fields', 'error');
            return;
        }

        // Validate Instagram URL
        if (!this.isValidInstagramUrl(reelData.url)) {
            this.showToast('Please enter a valid Instagram reel URL', 'error');
            return;
        }

        // Show loading
        this.showLoading(true);

        // Show specific message for ImgBB URLs
        const imgbbPattern = /^https?:\/\/ibb\.co\/([A-Za-z0-9]+)/;
        if (imgbbPattern.test(reelData.image)) {
            this.showToast('Processing ImgBB URL...', 'info');
        }

        try {
            // Process and validate image URL (handles ImgBB URLs automatically)
            const processedImageUrl = await this.processImageUrl(reelData.image);
            reelData.image = processedImageUrl;

            // Add reel to collection
            this.reels.unshift(reelData);
            this.saveReels();

            // Update UI
            this.renderReels();
            this.updateFilterTags();
            this.updateSwiper();
            this.closeAddModal();

            this.showToast('Reel added successfully!', 'success');
        } catch (error) {
            this.showToast(error.message || 'Invalid image URL. Please check the link.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // Validation Functions
    isValidInstagramUrl(url) {
        const instagramPattern = /^https?:\/\/(www\.)?instagram\.com\/(reel|p)\/[A-Za-z0-9_-]+/;
        return instagramPattern.test(url);
    }

    // Enhanced ImgBB URL handling - Simplified and more reliable
    async processImageUrl(url) {
        console.log('Processing URL:', url);

        // Check if it's an ImgBB URL
        const imgbbPattern = /^https?:\/\/(ibb\.co|i\.ibb\.co)/;
        const isImgBB = imgbbPattern.test(url);

        if (isImgBB) {
            console.log('ImgBB URL detected');

            // Convert to direct URL if needed
            const directUrl = this.convertImgBBUrl(url);
            console.log('Using direct URL:', directUrl);

            // Validate the direct URL
            try {
                await this.validateImageUrl(directUrl);
                return directUrl;
            } catch (error) {
                console.error('Direct URL validation failed, trying alternative formats');

                // Try alternative formats
                const imageId = this.extractImageId(url);
                if (imageId) {
                    const alternatives = [
                        `https://i.ibb.co/${imageId}.png`,
                        `https://i.ibb.co/${imageId}.jpeg`,
                        `https://i.ibb.co/${imageId}.webp`,
                        `https://i.ibb.co/${imageId}/image.jpg`,
                        `https://i.ibb.co/${imageId}/image.png`
                    ];

                    for (const altUrl of alternatives) {
                        try {
                            await this.validateImageUrl(altUrl);
                            console.log('Alternative URL worked:', altUrl);
                            return altUrl;
                        } catch (e) {
                            continue;
                        }
                    }
                }

                // If all else fails, return the converted URL anyway
                // (sometimes images load even if validation fails due to CORS)
                console.log('Validation failed but returning URL anyway');
                return directUrl;
            }
        }

        // If it's not ImgBB, validate as direct URL
        console.log('Validating non-ImgBB URL:', url);
        return this.validateImageUrl(url);
    }

    extractImageId(url) {
        const patterns = [
            /^https?:\/\/ibb\.co\/([A-Za-z0-9]+)/,
            /^https?:\/\/i\.ibb\.co\/([A-Za-z0-9]+)/
        ];

        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match) {
                return match[1];
            }
        }
        return null;
    }

    async getImgBBDirectUrl(shareUrl) {
        return new Promise((resolve, reject) => {
            console.log('Converting ImgBB URL:', shareUrl);

            // Extract the image ID from the sharing URL
            const imgbbPattern = /^https?:\/\/(ibb\.co|i\.ibb\.co)\/([A-Za-z0-9]+)/;
            const match = shareUrl.match(imgbbPattern);

            if (!match) {
                reject(new Error('Invalid ImgBB URL format'));
                return;
            }

            const imageId = match[2]; // Use index 2 since we have capturing groups
            console.log('Extracted image ID:', imageId);

            // If it's already a direct URL, try to validate it
            if (shareUrl.includes('i.ibb.co')) {
                console.log('Already a direct URL, validating...');
                const testImg = new Image();
                testImg.onload = () => {
                    console.log('Direct URL validated successfully');
                    resolve(shareUrl);
                };
                testImg.onerror = () => {
                    console.log('Direct URL validation failed');
                    reject(new Error('Direct image URL is not accessible'));
                };
                testImg.src = shareUrl;
                return;
            }

            // Try common ImgBB direct URL patterns
            const possibleUrls = [
                `https://i.ibb.co/${imageId}/image.png`,
                `https://i.ibb.co/${imageId}/image.jpg`,
                `https://i.ibb.co/${imageId}/image.jpeg`,
                `https://i.ibb.co/${imageId}.png`,
                `https://i.ibb.co/${imageId}.jpg`,
                `https://i.ibb.co/${imageId}.jpeg`,
                `https://i.ibb.co/${imageId}.webp`
            ];

            console.log('Testing possible URLs:', possibleUrls);

            // Test each possible URL
            let urlIndex = 0;
            const testUrl = () => {
                if (urlIndex >= possibleUrls.length) {
                    console.log('All URL patterns failed, trying fallback');
                    // Fallback: create a simple direct URL
                    const fallbackUrl = `https://i.ibb.co/${imageId}.jpg`;
                    resolve(fallbackUrl);
                    return;
                }

                const currentUrl = possibleUrls[urlIndex];
                console.log(`Testing URL ${urlIndex + 1}/${possibleUrls.length}:`, currentUrl);

                const testImg = new Image();
                testImg.onload = () => {
                    console.log('URL test successful:', currentUrl);
                    resolve(currentUrl);
                };
                testImg.onerror = () => {
                    console.log('URL test failed:', currentUrl);
                    urlIndex++;
                    setTimeout(testUrl, 100); // Small delay between tests
                };

                // Set a timeout for each test
                setTimeout(() => {
                    if (testImg.complete === false) {
                        console.log('URL test timeout:', currentUrl);
                        urlIndex++;
                        testUrl();
                    }
                }, 3000);

                testImg.src = currentUrl;
            };

            testUrl();
        });
    }

    // Simplified ImgBB URL converter - more reliable approach
    convertImgBBUrl(shareUrl) {
        console.log('Converting ImgBB URL with simple method:', shareUrl);

        // Extract image ID from various ImgBB URL formats
        let imageId = null;

        // Pattern 1: https://ibb.co/HTvR20N
        const pattern1 = /^https?:\/\/ibb\.co\/([A-Za-z0-9]+)/;
        const match1 = shareUrl.match(pattern1);
        if (match1) {
            imageId = match1[1];
        }

        // Pattern 2: https://i.ibb.co/HTvR20N/image.jpg (already direct)
        const pattern2 = /^https?:\/\/i\.ibb\.co\/([A-Za-z0-9]+)/;
        const match2 = shareUrl.match(pattern2);
        if (match2) {
            // Already a direct URL, return as-is
            return shareUrl;
        }

        if (imageId) {
            // Create the most common direct URL pattern
            const directUrl = `https://i.ibb.co/${imageId}.jpg`;
            console.log('Converted to direct URL:', directUrl);
            return directUrl;
        }

        // If no pattern matches, return original URL
        console.log('No ImgBB pattern matched, returning original URL');
        return shareUrl;
    }

    validateImageUrl(url) {
        return new Promise((resolve, reject) => {
            console.log('Validating image URL:', url);

            const img = new Image();

            // Set a timeout for validation
            const timeout = setTimeout(() => {
                console.log('Image validation timeout for:', url);
                // For ImgBB URLs, resolve anyway as they might work despite CORS issues
                if (url.includes('ibb.co')) {
                    console.log('ImgBB URL - resolving despite timeout');
                    resolve(url);
                } else {
                    reject(new Error('Image validation timeout'));
                }
            }, 5000);

            img.onload = () => {
                console.log('Image validation successful:', url);
                clearTimeout(timeout);
                resolve(url);
            };

            img.onerror = (error) => {
                console.log('Image validation failed:', url, error);
                clearTimeout(timeout);
                // For ImgBB URLs, be more lenient
                if (url.includes('ibb.co')) {
                    console.log('ImgBB URL - resolving despite error (might be CORS)');
                    resolve(url);
                } else {
                    reject(new Error('Image validation failed'));
                }
            };

            // Enable CORS for testing
            img.crossOrigin = 'anonymous';
            img.src = url;
        });
    }

    // Rendering Functions
    renderReels() {
        const container = document.getElementById('reelsGrid');
        const emptyState = document.getElementById('emptyState');
        
        const filteredReels = this.getFilteredReels();
        
        if (filteredReels.length === 0) {
            container.style.display = 'none';
            emptyState.classList.add('show');
            return;
        }
        
        container.style.display = 'grid';
        emptyState.classList.remove('show');
        
        container.innerHTML = filteredReels.map(reel => this.createReelCard(reel)).join('');
        
        // Add click events to cards
        container.querySelectorAll('.reel-card').forEach((card, index) => {
            card.addEventListener('click', () => this.openDetailModal(filteredReels[index]));
        });
        
        // Animate cards
        this.animateCards();
    }

    createReelCard(reel) {
        const formattedDate = new Date(reel.dateAdded).toLocaleDateString();
        const tagsHtml = reel.tags.slice(0, 3).map(tag => 
            `<span class="reel-tag">${tag}</span>`
        ).join('');
        
        return `
            <div class="reel-card fade-in">
                <img src="${reel.image}" alt="${reel.title}" class="reel-card-image" loading="lazy">
                <div class="reel-card-content">
                    <h3 class="reel-card-title">${reel.title}</h3>
                    <div class="reel-card-tags">
                        ${tagsHtml}
                        ${reel.tags.length > 3 ? `<span class="reel-tag">+${reel.tags.length - 3}</span>` : ''}
                    </div>
                    <div class="reel-card-meta">
                        <span class="reel-card-date">
                            <i class="fas fa-calendar-alt"></i>
                            ${formattedDate}
                        </span>
                        <div class="reel-card-actions">
                            <button class="card-action-btn" title="Open Link" onclick="event.stopPropagation(); window.open('${reel.url}', '_blank')">
                                <i class="fab fa-instagram"></i>
                            </button>
                            <button class="card-action-btn" title="Copy Link" onclick="event.stopPropagation(); reelsCollector.copyToClipboard('${reel.url}')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Search and Filter Functions
    handleSearch(e) {
        this.searchQuery = e.target.value.toLowerCase();
        this.renderReels();
    }

    getFilteredReels() {
        let filtered = this.reels;
        
        // Apply search filter
        if (this.searchQuery) {
            filtered = filtered.filter(reel => 
                reel.title.toLowerCase().includes(this.searchQuery) ||
                reel.tags.some(tag => tag.toLowerCase().includes(this.searchQuery))
            );
        }
        
        // Apply tag filter
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(reel => 
                reel.tags.some(tag => tag.toLowerCase() === this.currentFilter.toLowerCase())
            );
        }
        
        return filtered;
    }

    updateFilterTags() {
        const container = document.getElementById('filterTags');
        const allTags = [...new Set(this.reels.flatMap(reel => reel.tags))];
        
        container.innerHTML = `
            <span class="filter-tag ${this.currentFilter === 'all' ? 'active' : ''}" data-filter="all">
                All (${this.reels.length})
            </span>
            ${allTags.map(tag => `
                <span class="filter-tag ${this.currentFilter === tag ? 'active' : ''}" data-filter="${tag}">
                    ${tag} (${this.reels.filter(reel => reel.tags.includes(tag)).length})
                </span>
            `).join('')}
        `;
        
        // Add click events to filter tags
        container.querySelectorAll('.filter-tag').forEach(tag => {
            tag.addEventListener('click', () => {
                this.currentFilter = tag.dataset.filter;
                this.updateFilterTags();
                this.renderReels();
            });
        });
    }

    // Utility Functions
    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showToast('Link copied to clipboard!', 'success');
        }).catch(() => {
            this.showToast('Failed to copy link', 'error');
        });
    }

    downloadVideo(url) {
        const downloadUrl = `https://sssinstagram.com/reels-downloader?url=${encodeURIComponent(url)}`;
        window.open(downloadUrl, '_blank');
        this.showToast('Redirecting to download page...', 'info');
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };
        
        toast.innerHTML = `
            <i class="toast-icon ${icons[type]}"></i>
            <span class="toast-message">${message}</span>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        container.appendChild(toast);
        
        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => this.removeToast(toast), 5000);
        
        // Manual close
        toast.querySelector('.toast-close').addEventListener('click', () => this.removeToast(toast));
    }

    removeToast(toast) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }

    showLoading(show) {
        const spinner = document.getElementById('loadingSpinner');
        if (show) {
            spinner.classList.add('show');
        } else {
            spinner.classList.remove('show');
        }
    }

    animateCards() {
        const cards = document.querySelectorAll('.reel-card');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }

    initLottieAnimations() {
        // Initialize empty state animation
        const emptyLottie = document.getElementById('emptyLottie');
        if (emptyLottie) {
            emptyLottie.innerHTML = `
                <lottie-player
                    src="https://assets2.lottiefiles.com/packages/lf20_hl5n0bwb.json"
                    background="transparent"
                    speed="1"
                    style="width: 100%; height: 100%;"
                    loop
                    autoplay>
                </lottie-player>
            `;
        }
    }

    // Swiper Integration
    initSwiper() {
        if (typeof Swiper !== 'undefined') {
            this.swiper = new Swiper('.featured-swiper', {
                slidesPerView: 'auto',
                spaceBetween: 20,
                centeredSlides: false,
                loop: false,
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                    dynamicBullets: true,
                },
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
                breakpoints: {
                    320: {
                        slidesPerView: 1,
                        spaceBetween: 10,
                        centeredSlides: true,
                    },
                    640: {
                        slidesPerView: 2,
                        spaceBetween: 15,
                        centeredSlides: false,
                    },
                    768: {
                        slidesPerView: 3,
                        spaceBetween: 20,
                    },
                    1024: {
                        slidesPerView: 4,
                        spaceBetween: 20,
                    },
                },
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                effect: 'slide',
                speed: 600,
                watchOverflow: true,
            });
        }
        this.updateSwiper();
    }

    updateSwiper() {
        const featuredSection = document.getElementById('featuredSection');
        const featuredWrapper = document.getElementById('featuredWrapper');

        if (this.reels.length >= 3) {
            featuredSection.style.display = 'block';

            // Get the 6 most recent reels for the carousel
            const recentReels = this.reels.slice(0, 6);

            featuredWrapper.innerHTML = recentReels.map(reel => this.createFeaturedSlide(reel)).join('');

            // Update swiper after content change
            if (this.swiper) {
                this.swiper.update();
                this.swiper.updateSlides();
            }

            // Add click events to featured slides
            featuredWrapper.querySelectorAll('.featured-slide').forEach((slide, index) => {
                slide.addEventListener('click', () => this.openDetailModal(recentReels[index]));
            });
        } else {
            featuredSection.style.display = 'none';
        }
    }

    createFeaturedSlide(reel) {
        const tagsHtml = reel.tags.slice(0, 2).map(tag =>
            `<span class="featured-slide-tag">${tag}</span>`
        ).join('');

        return `
            <div class="swiper-slide">
                <div class="featured-slide">
                    <img src="${reel.image}" alt="${reel.title}" class="featured-slide-image" loading="lazy">
                    <div class="featured-slide-content">
                        <h4 class="featured-slide-title">${reel.title}</h4>
                        <div class="featured-slide-tags">
                            ${tagsHtml}
                            ${reel.tags.length > 2 ? `<span class="featured-slide-tag">+${reel.tags.length - 2}</span>` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // Enhanced Animation Functions
    animateCards() {
        const cards = document.querySelectorAll('.reel-card');

        // Use Intersection Observer for better performance
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    }

    // Enhanced Motion Effects
    addMotionEffects() {
        // Add parallax effect to header
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const header = document.querySelector('.header');
            if (header) {
                header.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Add hover effects to buttons
        document.querySelectorAll('.add-btn, .cta-btn, .action-btn').forEach(btn => {
            btn.addEventListener('mouseenter', () => {
                btn.style.transform = 'translateY(-2px) scale(1.02)';
            });

            btn.addEventListener('mouseleave', () => {
                btn.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // Additional Utility Functions
    exportReels() {
        const dataStr = JSON.stringify(this.reels, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `instagram-reels-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
        this.showToast('Reels exported successfully!', 'success');
    }

    importReels(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const importedReels = JSON.parse(e.target.result);
                if (Array.isArray(importedReels)) {
                    this.reels = [...this.reels, ...importedReels];
                    this.saveReels();
                    this.renderReels();
                    this.updateFilterTags();
                    this.updateSwiper();
                    this.showToast(`Imported ${importedReels.length} reels successfully!`, 'success');
                } else {
                    throw new Error('Invalid file format');
                }
            } catch (error) {
                this.showToast('Failed to import reels. Please check the file format.', 'error');
            }
        };
        reader.readAsText(file);
    }

    deleteReel(reelId) {
        if (confirm('Are you sure you want to delete this reel?')) {
            this.reels = this.reels.filter(reel => reel.id !== reelId);
            this.saveReels();
            this.renderReels();
            this.updateFilterTags();
            this.updateSwiper();
            this.closeDetailModal();
            this.showToast('Reel deleted successfully!', 'success');
        }
    }

    // Error Handling
    handleError(error, context = 'Unknown') {
        console.error(`Error in ${context}:`, error);
        this.showToast(`An error occurred: ${error.message}`, 'error');
    }

    // Performance Monitoring
    measurePerformance(name, fn) {
        const start = performance.now();
        const result = fn();
        const end = performance.now();
        console.log(`${name} took ${end - start} milliseconds`);
        return result;
    }

    // Demo function to test ImgBB URL processing
    async testImgBBUrl(shareUrl) {
        try {
            console.log('Testing ImgBB URL:', shareUrl);
            const directUrl = await this.processImageUrl(shareUrl);
            console.log('Converted to direct URL:', directUrl);
            this.showToast('ImgBB URL processed successfully!', 'success');
            return directUrl;
        } catch (error) {
            console.error('ImgBB URL processing failed:', error);
            this.showToast('Failed to process ImgBB URL', 'error');
            throw error;
        }
    }
}

// Initialize the application
let reelsCollector;
document.addEventListener('DOMContentLoaded', () => {
    reelsCollector = new ReelsCollector();
});

// Global test function for ImgBB URLs
async function testImgBB() {
    const testUrl = prompt('Enter an ImgBB URL to test (e.g., https://ibb.co/HTvR20N):');
    if (testUrl && reelsCollector) {
        try {
            console.log('Testing URL:', testUrl);
            reelsCollector.showToast('Testing ImgBB URL...', 'info');
            const result = await reelsCollector.processImageUrl(testUrl);
            console.log('Test result:', result);
            reelsCollector.showToast(`Success! Direct URL: ${result}`, 'success');

            // Show the result in a new window for visual confirmation
            const newWindow = window.open('', '_blank', 'width=600,height=400');
            newWindow.document.write(`
                <html>
                    <head><title>ImgBB Test Result</title></head>
                    <body style="font-family: Arial; padding: 20px;">
                        <h2>ImgBB URL Test Result</h2>
                        <p><strong>Original URL:</strong> ${testUrl}</p>
                        <p><strong>Direct URL:</strong> ${result}</p>
                        <img src="${result}" style="max-width: 100%; border: 1px solid #ccc; border-radius: 8px;"
                             onload="document.getElementById('status').innerHTML='✅ Image loaded successfully!'; document.getElementById('status').style.color='green';"
                             onerror="document.getElementById('status').innerHTML='❌ Image failed to load'; document.getElementById('status').style.color='red';">
                        <p id="status">Loading image...</p>
                    </body>
                </html>
            `);
        } catch (error) {
            console.error('Test failed:', error);
            reelsCollector.showToast(`Test failed: ${error.message}`, 'error');
        }
    }
}
