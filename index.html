<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Reels Collector</title>
    
    <!-- External Libraries -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fab fa-instagram"></i>
                    <h1>Reels Collector</h1>
                </div>
                <div style="display: flex; gap: 1rem; align-items: center;">
                    <button class="add-btn" id="addReelBtn">
                        <i class="fas fa-plus"></i>
                        Add Link
                    </button>
                    <button class="add-btn" onclick="testImgBB()" style="background: rgba(255,255,255,0.1); font-size: 0.9rem;">
                        🧪 Test ImgBB
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Search and Filter Section -->
            <section class="search-section">
                <div class="search-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search reels by title or tags...">
                    </div>
                    <div class="filter-tags" id="filterTags">
                        <!-- Dynamic filter tags will be inserted here -->
                    </div>
                </div>
            </section>

            <!-- Empty State -->
            <section class="empty-state" id="emptyState">
                <div class="empty-content">
                    <div class="lottie-container" id="emptyLottie"></div>
                    <h2>No Reels Yet</h2>
                    <p>Start building your collection by adding your first Instagram reel!</p>
                    <button class="cta-btn" onclick="document.getElementById('addReelBtn').click()">
                        <i class="fas fa-plus"></i>
                        Add Your First Reel
                    </button>
                </div>
            </section>

            <!-- Featured Reels Carousel -->
            <section class="featured-section" id="featuredSection" style="display: none;">
                <h2 class="section-title">
                    <i class="fas fa-star"></i>
                    Recent Additions
                </h2>
                <div class="swiper featured-swiper">
                    <div class="swiper-wrapper" id="featuredWrapper">
                        <!-- Featured reel slides will be inserted here -->
                    </div>
                    <div class="swiper-pagination"></div>
                    <div class="swiper-button-next"></div>
                    <div class="swiper-button-prev"></div>
                </div>
            </section>

            <!-- Reels Grid -->
            <section class="reels-grid" id="reelsGrid">
                <!-- Dynamic reel cards will be inserted here -->
            </section>
        </div>
    </main>

    <!-- Add Reel Modal -->
    <div class="modal-overlay" id="addReelModal">
        <div class="modal">
            <div class="modal-header">
                <h2>Add New Reel</h2>
                <button class="close-btn" id="closeModalBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form class="modal-form" id="addReelForm">
                <div class="form-group">
                    <label for="reelTitle">Title *</label>
                    <input type="text" id="reelTitle" name="title" required placeholder="Enter reel title">
                </div>
                
                <div class="form-group">
                    <label for="reelImage">Image Preview URL *</label>
                    <textarea id="reelImage" name="image" required placeholder="Paste any of these:
1. ImgBB sharing URL: https://ibb.co/HTvR20N
2. Direct image URL: https://i.ibb.co/xxx.jpg
3. ImgBB HTML code: <img src='...' alt='...' />"></textarea>
                    <small>
                        <strong>Option 1:</strong> ImgBB sharing URL (https://ibb.co/xxxxx)<br>
                        <strong>Option 2:</strong> Direct image URL (.jpg, .png, etc.)<br>
                        <strong>Option 3:</strong> ImgBB HTML code - we'll extract the src automatically
                    </small>
                </div>
                
                <div class="form-group">
                    <label for="reelUrl">Instagram Reel URL *</label>
                    <input type="url" id="reelUrl" name="url" required placeholder="https://www.instagram.com/reel/...">
                </div>
                
                <div class="form-group">
                    <label for="reelTags">Tags</label>
                    <input type="text" id="reelTags" name="tags" placeholder="Enter tags separated by commas">
                    <small>e.g., funny, dance, cooking, travel</small>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="cancel-btn" id="cancelBtn">Cancel</button>
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-save"></i>
                        Save Reel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Reel Detail Modal -->
    <div class="modal-overlay" id="reelDetailModal">
        <div class="detail-modal">
            <button class="close-btn detail-close" id="closeDetailBtn">
                <i class="fas fa-times"></i>
            </button>
            <div class="detail-content">
                <div class="detail-left">
                    <div class="image-container">
                        <img id="detailImage" src="" alt="Reel preview">
                        <div class="zoom-overlay"></div>
                    </div>
                </div>
                <div class="detail-right">
                    <div class="detail-info">
                        <h2 id="detailTitle"></h2>
                        <div class="detail-tags" id="detailTags"></div>
                        <div class="detail-actions">
                            <button class="action-btn primary" id="openLinkBtn">
                                <i class="fab fa-instagram"></i>
                                Open Link
                            </button>
                            <button class="action-btn secondary" id="copyLinkBtn">
                                <i class="fas fa-copy"></i>
                                Copy Link
                            </button>
                            <div class="download-container">
                                <button class="action-btn download" id="downloadBtn">
                                    <img src="https://sssinstagram.com/favicon.ico" alt="SSS" width="16" height="16">
                                    Download Video
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                                <div class="download-dropdown" id="downloadDropdown">
                                    <div class="dropdown-content">
                                        <h4>📥 Download Instructions</h4>
                                        <ol>
                                            <li>
                                                <strong>Step 1:</strong> Copy the Instagram link
                                                <button class="copy-link-btn" onclick="copyInstagramLink()">
                                                    <i class="fas fa-copy"></i> Copy Link
                                                </button>
                                            </li>
                                            <li><strong>Step 2:</strong> Click "Download Video" below</li>
                                            <li><strong>Step 3:</strong> Paste the link on SSS Instagram</li>
                                            <li><strong>Step 4:</strong> Click "Go to Download Page"</li>
                                        </ol>
                                        <div class="dropdown-actions">
                                            <button class="dropdown-btn copy" onclick="copyInstagramLink()">
                                                <i class="fas fa-copy"></i> Copy Link
                                            </button>
                                            <button class="dropdown-btn download" onclick="goToDownloadPage()">
                                                <i class="fas fa-download"></i> Go to Download Page
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="detail-meta">
                            <p><strong>Added:</strong> <span id="detailDate"></span></p>
                            <p><strong>URL:</strong> <span id="detailUrl"></span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner"></div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- External Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>

    <!-- Service Worker Registration -->
    <script>
        // Register service worker for better performance (optional)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js').catch(() => {
                    // Service worker registration failed, but app will still work
                });
            });
        }
    </script>
    
    <!-- Custom Scripts -->
    <script src="script.js"></script>
</body>
</html>
